# Test info

- Name: Editor Scrolling >> should preserve collaborative features during scrolling
- Location: /Users/<USER>/Developer/collaborative_content_editor_ko8hwk/src/__tests__/integration/editor-scrolling.integration.test.ts:92:3

# Error details

```
Error: page.click: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('button:has-text("New Document")')

    at /Users/<USER>/Developer/collaborative_content_editor_ko8hwk/src/__tests__/integration/editor-scrolling.integration.test.ts:94:16
```

# Page snapshot

```yaml
- banner:
  - heading "Collaborative Editor" [level=2]
  - button "Sign out"
- main:
  - heading "My Documents" [level=1]
  - paragraph: Limited Access
  - paragraph: Anonymous users can only view shared documents.
  - heading "No documents yet" [level=3]
  - paragraph: Sign up for an account or ask someone to share a document with you.
  - heading "No Documents Available" [level=2]
  - paragraph: Click "New Document" to create your first document
  - text: Real-time collaboration Rich text editing
- region "Notifications alt+T"
```

# Test source

```ts
   1 | /**
   2 |  * Integration Tests for Editor Scrolling Functionality
   3 |  * Tests that the ProseMirror editor properly handles scrolling for long documents
   4 |  */
   5 |
   6 | import { test, expect } from '@playwright/test';
   7 |
   8 | test.describe('Editor Scrolling', () => {
   9 |   test.beforeEach(async ({ page }) => {
   10 |     await page.goto('/');
   11 |     
   12 |     // Sign in anonymously to access editor features
   13 |     await page.click('button:has-text("Sign in anonymously")');
   14 |     await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });
   15 |   });
   16 |
   17 |   test('should allow scrolling when document content exceeds viewport height', async ({ page }) => {
   18 |     // Create a new document
   19 |     await page.click('button:has-text("New Document")');
   20 |     await page.waitForSelector('.prose', { timeout: 5000 });
   21 |
   22 |     // Add a large amount of content to trigger scrolling
   23 |     const editor = page.locator('.prose');
   24 |     await editor.click();
   25 |     
   26 |     // Add multiple paragraphs to create a long document
   27 |     const longContent = Array.from({ length: 50 }, (_, i) => 
   28 |       `This is paragraph ${i + 1}. It contains enough text to make the document quite long and test the scrolling functionality. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.`
   29 |     ).join('\n\n');
   30 |     
   31 |     await page.keyboard.type(longContent);
   32 |
   33 |     // Wait for content to be rendered
   34 |     await page.waitForTimeout(1000);
   35 |
   36 |     // Check that the editor container has scrollable area
   37 |     const scrollArea = page.locator('[data-radix-scroll-area-viewport]');
   38 |     await expect(scrollArea).toBeVisible();
   39 |
   40 |     // Verify that the scroll area has proper height constraints
   41 |     const scrollAreaBox = await scrollArea.boundingBox();
   42 |     expect(scrollAreaBox).toBeTruthy();
   43 |     expect(scrollAreaBox!.height).toBeGreaterThan(0);
   44 |     expect(scrollAreaBox!.height).toBeLessThan(2000); // Should be constrained, not unlimited
   45 |
   46 |     // Test scrolling functionality
   47 |     const initialScrollTop = await scrollArea.evaluate(el => el.scrollTop);
   48 |     
   49 |     // Scroll down
   50 |     await scrollArea.evaluate(el => el.scrollTo(0, 500));
   51 |     await page.waitForTimeout(100);
   52 |     
   53 |     const newScrollTop = await scrollArea.evaluate(el => el.scrollTop);
   54 |     expect(newScrollTop).toBeGreaterThan(initialScrollTop);
   55 |
   56 |     // Verify content is still accessible after scrolling
   57 |     await expect(page.locator('text=This is paragraph 1')).toBeVisible();
   58 |     await expect(page.locator('text=This is paragraph 50')).toBeVisible();
   59 |   });
   60 |
   61 |   test('should maintain cursor position and selection during scroll', async ({ page }) => {
   62 |     // Create a new document
   63 |     await page.click('button:has-text("New Document")');
   64 |     await page.waitForSelector('.prose', { timeout: 5000 });
   65 |
   66 |     const editor = page.locator('.prose');
   67 |     await editor.click();
   68 |     
   69 |     // Add content that will require scrolling
   70 |     const content = Array.from({ length: 30 }, (_, i) => 
   71 |       `Line ${i + 1}: This is a test line with enough content to test scrolling behavior.`
   72 |     ).join('\n');
   73 |     
   74 |     await page.keyboard.type(content);
   75 |
   76 |     // Position cursor in the middle of the document
   77 |     await page.keyboard.press('Control+Home'); // Go to start
   78 |     for (let i = 0; i < 15; i++) {
   79 |       await page.keyboard.press('ArrowDown');
   80 |     }
   81 |
   82 |     // Get scroll area and scroll down
   83 |     const scrollArea = page.locator('[data-radix-scroll-area-viewport]');
   84 |     await scrollArea.evaluate(el => el.scrollTo(0, 300));
   85 |     await page.waitForTimeout(100);
   86 |
   87 |     // Verify cursor is still functional
   88 |     await page.keyboard.type(' [CURSOR TEST]');
   89 |     await expect(page.locator('text=[CURSOR TEST]')).toBeVisible();
   90 |   });
   91 |
   92 |   test('should preserve collaborative features during scrolling', async ({ page }) => {
   93 |     // Create a new document
>  94 |     await page.click('button:has-text("New Document")');
      |                ^ Error: page.click: Test timeout of 30000ms exceeded.
   95 |     await page.waitForSelector('.prose', { timeout: 5000 });
   96 |
   97 |     const editor = page.locator('.prose');
   98 |     await editor.click();
   99 |     
  100 |     // Add content
  101 |     await page.keyboard.type('This is a test document for collaborative features during scrolling.');
  102 |
  103 |     // Select text for commenting
  104 |     await page.keyboard.press('Control+a');
  105 |
  106 |     // Wait for formatting toolbar and add comment button
  107 |     await page.waitForSelector('button:has-text("Add Comment")', { timeout: 3000 });
  108 |     
  109 |     // Scroll the editor area
  110 |     const scrollArea = page.locator('[data-radix-scroll-area-viewport]');
  111 |     await scrollArea.evaluate(el => el.scrollTo(0, 100));
  112 |     await page.waitForTimeout(100);
  113 |
  114 |     // Verify comment functionality still works after scrolling
  115 |     const addCommentButton = page.locator('button:has-text("Add Comment")');
  116 |     await expect(addCommentButton).toBeVisible();
  117 |     
  118 |     // Click add comment button
  119 |     await addCommentButton.click();
  120 |
  121 |     // Verify comment popover appears
  122 |     await page.waitForSelector('[data-testid="comment-popover"]', { timeout: 3000 });
  123 |     await expect(page.locator('[data-testid="comment-popover"]')).toBeVisible();
  124 |   });
  125 |
  126 |   test('should handle keyboard navigation in scrollable editor', async ({ page }) => {
  127 |     // Create a new document
  128 |     await page.click('button:has-text("New Document")');
  129 |     await page.waitForSelector('.prose', { timeout: 5000 });
  130 |
  131 |     const editor = page.locator('.prose');
  132 |     await editor.click();
  133 |     
  134 |     // Add content that requires scrolling
  135 |     const content = Array.from({ length: 40 }, (_, i) => 
  136 |       `Paragraph ${i + 1}: Testing keyboard navigation in a scrollable editor environment.`
  137 |     ).join('\n\n');
  138 |     
  139 |     await page.keyboard.type(content);
  140 |
  141 |     // Test keyboard navigation
  142 |     await page.keyboard.press('Control+Home'); // Go to start
  143 |     await page.keyboard.press('Control+End');   // Go to end
  144 |     
  145 |     // Verify we can still type at the end
  146 |     await page.keyboard.type('\n\nFinal paragraph added via keyboard navigation.');
  147 |     await expect(page.locator('text=Final paragraph added via keyboard navigation.')).toBeVisible();
  148 |
  149 |     // Test page up/down navigation
  150 |     await page.keyboard.press('PageUp');
  151 |     await page.keyboard.press('PageDown');
  152 |     
  153 |     // Verify editor is still functional
  154 |     await page.keyboard.type(' [NAVIGATION TEST]');
  155 |     await expect(page.locator('text=[NAVIGATION TEST]')).toBeVisible();
  156 |   });
  157 |
  158 |   test('should maintain proper focus management in scrollable area', async ({ page }) => {
  159 |     // Create a new document
  160 |     await page.click('button:has-text("New Document")');
  161 |     await page.waitForSelector('.prose', { timeout: 5000 });
  162 |
  163 |     const editor = page.locator('.prose');
  164 |     await editor.click();
  165 |     
  166 |     // Add content
  167 |     await page.keyboard.type('Testing focus management in scrollable editor.');
  168 |
  169 |     // Verify editor has focus
  170 |     const focusedElement = await page.evaluate(() => document.activeElement?.tagName);
  171 |     expect(['DIV', 'TEXTAREA', 'INPUT'].includes(focusedElement || '')).toBeTruthy();
  172 |
  173 |     // Scroll and verify focus is maintained
  174 |     const scrollArea = page.locator('[data-radix-scroll-area-viewport]');
  175 |     await scrollArea.evaluate(el => el.scrollTo(0, 200));
  176 |     await page.waitForTimeout(100);
  177 |
  178 |     // Type more content to verify focus is still active
  179 |     await page.keyboard.type(' Additional content after scroll.');
  180 |     await expect(page.locator('text=Additional content after scroll.')).toBeVisible();
  181 |   });
  182 | });
  183 |
```