/**
 * Unit tests for CollaborativeEditor scrolling functionality
 */

import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { CollaborativeEditor } from '../CollaborativeEditor';

// Mock the Convex hooks and dependencies
vi.mock('convex/react', () => ({
  useQuery: vi.fn(() => ({ permission: 'write' })),
  useMutation: vi.fn(() => vi.fn()),
}));

vi.mock('@convex-dev/prosemirror-sync/blocknote', () => ({
  useBlockNoteSync: vi.fn(() => ({
    editor: {
      prosemirrorView: {
        state: {
          plugins: [],
        },
      },
    },
    isLoading: false,
  })),
}));

vi.mock('../hooks/useCollaborativeCursors', () => ({
  useCollaborativeCursors: vi.fn(),
}));

vi.mock('../hooks/useComments', () => ({
  useComments: vi.fn(() => ({
    comments: [],
    createComment: vi.fn(),
    updateComment: vi.fn(),
    deleteComment: vi.fn(),
    createReply: vi.fn(),
    updateReply: vi.fn(),
    deleteReply: vi.fn(),
    resolveComment: vi.fn(),
    selectComment: vi.fn(),
    getPluginState: vi.fn(() => ({})),
    forceRefreshDecorations: vi.fn(),
    debugCommentState: vi.fn(),
  })),
}));

vi.mock('../DocumentHeader', () => ({
  DocumentHeader: () => <div data-testid="document-header">Document Header</div>,
}));

vi.mock('../CommentSidebar', () => ({
  CommentSidebar: () => <div data-testid="comment-sidebar">Comment Sidebar</div>,
}));

vi.mock('../CommentPopover', () => ({
  CommentPopover: () => <div data-testid="comment-popover">Comment Popover</div>,
}));

// Mock BlockNote components
vi.mock('@blocknote/mantine', () => ({
  BlockNoteView: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="blocknote-view" className="prose prose-sm max-w-none">
      {children}
    </div>
  ),
}));

vi.mock('@blocknote/react', () => ({
  FormattingToolbarController: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="formatting-toolbar">{children}</div>
  ),
  FormattingToolbar: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  BlockTypeSelect: () => <div>BlockTypeSelect</div>,
  BasicTextStyleButton: () => <div>BasicTextStyleButton</div>,
  TextAlignButton: () => <div>TextAlignButton</div>,
  ColorStyleButton: () => <div>ColorStyleButton</div>,
  NestBlockButton: () => <div>NestBlockButton</div>,
  UnnestBlockButton: () => <div>UnnestBlockButton</div>,
  CreateLinkButton: () => <div>CreateLinkButton</div>,
  FileCaptionButton: () => <div>FileCaptionButton</div>,
  FileReplaceButton: () => <div>FileReplaceButton</div>,
  useComponentsContext: vi.fn(),
}));

describe('CollaborativeEditor Scrolling', () => {
  const mockDocumentId = 'test-document-id' as any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render with ScrollArea component', () => {
    render(<CollaborativeEditor documentId={mockDocumentId} />);
    
    // Check that the ScrollArea is rendered with proper attributes
    const scrollArea = document.querySelector('[data-radix-scroll-area-root]');
    expect(scrollArea).toBeInTheDocument();
  });

  it('should have proper height constraints on ScrollArea', () => {
    render(<CollaborativeEditor documentId={mockDocumentId} />);
    
    // Check that the ScrollArea has the expected CSS classes
    const scrollArea = document.querySelector('[data-radix-scroll-area-root]');
    expect(scrollArea).toHaveClass('h-[calc(100vh-300px)]');
    expect(scrollArea).toHaveClass('min-h-[500px]');
    expect(scrollArea).toHaveClass('editor-scroll-area');
  });

  it('should render BlockNoteView inside ScrollArea', () => {
    render(<CollaborativeEditor documentId={mockDocumentId} />);
    
    // Check that BlockNoteView is rendered
    const blockNoteView = screen.getByTestId('blocknote-view');
    expect(blockNoteView).toBeInTheDocument();
    
    // Check that it's inside a scrollable container
    const scrollArea = document.querySelector('[data-radix-scroll-area-root]');
    expect(scrollArea).toBeInTheDocument();
    expect(scrollArea).toContainElement(blockNoteView);
  });

  it('should maintain proper padding inside ScrollArea', () => {
    render(<CollaborativeEditor documentId={mockDocumentId} />);
    
    // Check that the content wrapper has proper padding
    const contentWrapper = document.querySelector('.p-6');
    expect(contentWrapper).toBeInTheDocument();
    
    // Check that BlockNoteView is inside the padded container
    const blockNoteView = screen.getByTestId('blocknote-view');
    expect(contentWrapper).toContainElement(blockNoteView);
  });

  it('should render scroll bars when content overflows', () => {
    render(<CollaborativeEditor documentId={mockDocumentId} />);
    
    // Check that scroll bar elements are present
    const scrollbar = document.querySelector('[data-radix-scroll-area-scrollbar]');
    expect(scrollbar).toBeInTheDocument();
  });

  it('should preserve collaborative features in scrollable container', () => {
    render(<CollaborativeEditor documentId={mockDocumentId} />);
    
    // Check that collaborative features are still rendered
    expect(screen.getByTestId('document-header')).toBeInTheDocument();
    expect(screen.getByTestId('comment-sidebar')).toBeInTheDocument();
    expect(screen.getByTestId('comment-popover')).toBeInTheDocument();
    
    // Check that the editor is still functional
    expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
  });
});
